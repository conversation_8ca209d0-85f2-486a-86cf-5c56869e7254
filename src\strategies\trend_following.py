"""
Trend Following Trading Strategy
"""

from typing import Dict, Any, List
from strategies.base_strategy import BaseStrategy, StrategyType, MarketData, TradingSignal
from logging_system.logger import logger

class TrendFollowingStrategy(BaseStrategy):
    """
    Trend Following Strategy
    Identifies and follows market trends using multiple timeframe analysis
    """
    
    def get_strategy_type(self) -> StrategyType:
        return StrategyType.TREND_FOLLOWING
    
    def get_strategy_name(self) -> str:
        return "Advanced Trend Following"
    
    def get_ai_prompt(
        self, 
        market_data: MarketData,
        trade_history: List[Dict[str, Any]],
        account_info: Dict[str, Any]
    ) -> str:
        """Get AI prompt for trend following strategy"""
        
        recent_trades = trade_history[-10:] if len(trade_history) > 10 else trade_history
        win_rate = self._calculate_win_rate(recent_trades)
        avg_hold_time = self._calculate_avg_hold_time(recent_trades)
        
        prompt = f"""
You are executing a TREND FOLLOWING trading strategy for {market_data.symbol} on {market_data.timeframe} timeframe.
*** YOU ARE AN EXPERIENCED PROFITABLE TRADER MANAGING A REAL ACCOUNT - ONLY TAKE HIGH-PROBABILITY TRADES ***

CRITICAL SUCCESS FACTORS:
- ONLY trade when trend is CRYSTAL CLEAR and strong
- WAIT for perfect setups - patience is key to profitability
- NEVER trade against the trend or in choppy markets
- ALWAYS use proper risk management with tight stops
- Focus on 1:3 or better risk-reward ratios

STRATEGY OVERVIEW:
- Strategy Type: Trend Following
- Magic Number: {self.magic_number}
- Focus: Identify and ride strong directional moves
- Timeframe: {market_data.timeframe}
- Symbol: {market_data.symbol}

MARKET DATA ANALYSIS:
- Current Price: {market_data.current_price}
- Spread: {market_data.spread} pips
- Volume: {market_data.volume}
- Volatility: {market_data.volatility:.5f}

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Average Hold Time: {avg_hold_time:.1f} hours
- Total Recent Trades: {len(recent_trades)}

TREND FOLLOWING PRINCIPLES:
1. "The trend is your friend" - trade in direction of established trends
2. Use multiple timeframe confirmation (higher TF for trend, lower TF for entry)
3. Enter on pullbacks in trending markets
4. Let profits run, cut losses short
5. Avoid choppy, sideways markets

TECHNICAL ANALYSIS FOCUS:
- Moving averages (20, 50, 200 EMA) for trend direction
- Price action patterns (higher highs/lows for uptrend, lower highs/lows for downtrend)
- Support and resistance levels
- Volume confirmation
- Momentum indicators (RSI, MACD) for entry timing

STRICT ENTRY CRITERIA (ALL MUST BE MET):
1. STRONG trend established on higher timeframe (clear direction for 20+ candles)
2. Price pullback to key moving average (20 EMA or 50 EMA) - NOT against trend
3. Momentum indicators (RSI, MACD) confirming trend direction
4. Volume increasing in trend direction
5. Risk-reward ratio MINIMUM 1:3 (prefer 1:4 or better)
6. Clear support/resistance levels for stop placement
7. NO major news events in next 4 hours

CONSERVATIVE EXIT CRITERIA:
1. Take profit at 75% of target when 1:2 RR achieved (secure profits)
2. Trail stop loss to breakeven when 1:1 RR achieved
3. Exit immediately on trend reversal signals
4. Exit on momentum divergence or volume decline
5. NEVER hold through major news events

RISK MANAGEMENT:
- Stop loss below/above recent swing point (consider spread)
- Position size based on volatility (ATR)
- Maximum 3 correlated positions
- Avoid trading during major news events

MARKET CONDITIONS TO AVOID:
- Choppy, sideways markets (low ADX)
- High impact news events
- Low volume periods
- Excessive spread conditions

Based on the 200 candles of market data provided, analyze:
1. Overall trend direction and strength
2. Current market structure (trending vs ranging)
3. Key support and resistance levels
4. Entry opportunities with specific prices
5. Stop loss and take profit levels
6. Position sizing recommendations
7. Market timing and session considerations

Provide specific trading signals with:
- Action: BUY/SELL/HOLD/CLOSE with reasoning
- Entry price and timing
- Stop loss level (mandatory)
- Take profit targets (multiple levels if appropriate)
- Confidence level (0-100%)
- Risk assessment (LOW/MEDIUM/HIGH)

Remember: Trend following requires patience. Wait for clear setups and let winners run.
"""
        return prompt
    
    def validate_signal(self, signal: TradingSignal, market_data: MarketData) -> bool:
        """Validate trend following signal with strict profitability criteria"""

        # Must have stop loss for trend following
        if not signal.stop_loss:
            logger.warning(f"Trend following signal rejected: missing stop_loss")
            return False

        # Take profit is recommended but not required (can be set by money management)
        if not signal.take_profit:
            logger.info(f"Trend following signal: no take_profit specified, will use default risk/reward ratio")
            # Don't reject the signal, just log it

        # High confidence required for profitability (70%+ for testing)
        if signal.confidence < 0.7:
            logger.warning(f"Trend following signal rejected: confidence {signal.confidence} < 0.7")
            return False

        # Spread conditions (max 2.0 pips for testing)
        max_spread = self.config.get('max_spread', 2.0)
        if market_data.spread > max_spread:
            logger.warning(f"Trend following signal rejected: spread {market_data.spread} > {max_spread}")
            return False

        # Strict risk-reward ratio check (minimum 1:3) - only if we have take_profit
        if signal.entry_price and signal.stop_loss and signal.take_profit:
            risk = abs(signal.entry_price - signal.stop_loss)
            reward = abs(signal.take_profit - signal.entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            logger.info(f"Risk-reward ratio: {risk_reward_ratio:.2f} (risk: {risk:.5f}, reward: {reward:.5f})")
            if risk_reward_ratio < 1.0:  # Minimum 1:1 RR for profitability (very lenient for testing)
                logger.warning(f"Trend following signal rejected: risk-reward ratio {risk_reward_ratio:.2f} < 1.0")
                return False

        # Reject signals with excessive risk (stop loss too far)
        if signal.entry_price and signal.stop_loss and market_data.pip_size:
            risk_pips = abs(signal.entry_price - signal.stop_loss) / market_data.pip_size
            logger.info(f"Risk in pips: {risk_pips:.1f}")
            if risk_pips > 30:  # Max 30 pips risk for trend following
                logger.warning(f"Trend following signal rejected: risk {risk_pips:.1f} pips > 30 pips")
                return False

        logger.info(f"Trend following signal validation PASSED")
        return True
    
    def _calculate_win_rate(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100
    
    def _calculate_avg_hold_time(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate average hold time in hours"""
        if not trades:
            return 0.0
        
        total_time = sum(trade.get('hold_time_hours', 0) for trade in trades)
        return total_time / len(trades)
