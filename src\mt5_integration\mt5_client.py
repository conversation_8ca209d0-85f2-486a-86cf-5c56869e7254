"""
MetaTrader 5 Client for Trading Operations
"""

import os
import time
import MetaTrader5 as mt5
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from logging_system.logger import get_logger, trading_logger
from account_management.models import TradingAccount, AccountBalance

logger = get_logger(__name__)

class MT5Client:
    """MetaTrader 5 client for trading operations"""
    
    def __init__(self):
        self.mt5_path = os.getenv('MT5_PATH', r'C:\Program Files\MetaTrader 5\terminal64.exe')
        self.current_account = None
        self.connected = False
    
    def initialize(self) -> bool:
        """Initialize MT5 connection with intensive debugging"""
        logger.info(f"🔍 INIT_DEBUG: Starting MT5 initialization with path: {self.mt5_path}")

        try:
            # Check if MT5 path exists
            if not os.path.exists(self.mt5_path):
                error_msg = f"❌ INIT_DEBUG: MT5 executable not found at path: {self.mt5_path}"
                logger.error(error_msg)
                return False

            logger.info(f"🔍 INIT_DEBUG: MT5 executable found at: {self.mt5_path}")

            # Check if already initialized
            terminal_info = mt5.terminal_info()
            if terminal_info is not None:
                logger.info(f"🔍 INIT_DEBUG: MT5 already initialized. Connected: {terminal_info.connected}")
                if terminal_info.connected:
                    self.connected = True
                    logger.info("✅ INIT_DEBUG: Using existing MT5 connection")
                    return True
                else:
                    logger.warning("⚠️ INIT_DEBUG: MT5 initialized but not connected")

            # Check if we can get account info (another way to verify connection)
            try:
                account_info = mt5.account_info()
                if account_info is not None:
                    logger.info(f"✅ INIT_DEBUG: MT5 already connected to account {account_info.login}")
                    self.connected = True
                    return True
            except Exception as e:
                logger.debug(f"🔍 INIT_DEBUG: Cannot get account info: {e}")

            # Initialize MT5
            logger.info("🔍 INIT_DEBUG: Calling mt5.initialize()...")
            if not mt5.initialize(path=self.mt5_path):
                error = mt5.last_error()
                error_msg = f"❌ INIT_DEBUG: Failed to initialize MT5. Error: {error}"
                logger.error(error_msg)

                # Try to get more information about the failure
                try:
                    terminal_info = mt5.terminal_info()
                    if terminal_info:
                        logger.error(f"🔍 INIT_DEBUG: Terminal info after failed init - Connected: {terminal_info.connected}, Build: {terminal_info.build}")
                    else:
                        logger.error("🔍 INIT_DEBUG: Cannot get terminal info after failed initialization")
                except:
                    logger.error("🔍 INIT_DEBUG: Exception getting terminal info after failed init")

                return False

            # Verify initialization
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                error_msg = "❌ INIT_DEBUG: Initialization appeared successful but cannot get terminal info"
                logger.error(error_msg)
                return False

            logger.info(f"✅ INIT_DEBUG: MT5 initialized successfully")
            logger.info(f"🔍 INIT_DEBUG: Terminal info - Connected: {terminal_info.connected}, Build: {terminal_info.build}, Company: {terminal_info.company}")
            logger.info(f"🔍 INIT_DEBUG: Terminal path: {terminal_info.path}, Data path: {terminal_info.data_path}")

            self.connected = True
            return True

        except Exception as e:
            error_msg = f"❌ INIT_DEBUG: Exception during initialization: {type(e).__name__}: {e}"
            logger.error(error_msg)
            logger.error(f"🔍 INIT_DEBUG: Exception traceback:", exc_info=True)
            return False
    
    def shutdown(self) -> None:
        """Shutdown MT5 connection"""
        try:
            mt5.shutdown()
            self.connected = False
            self.current_account = None
            logger.info("MT5 connection closed")
        except Exception as e:
            logger.error(f"Error shutting down MT5: {e}")
    
    def login(self, account: TradingAccount) -> bool:
        """Login to specific trading account with intensive debugging"""
        logger.info(f"🔍 LOGIN_DEBUG: Starting login process for account {account.account_id} ({account.account_number})")

        try:
            # Check if MT5 is initialized
            if not self.connected:
                logger.info("🔍 LOGIN_DEBUG: MT5 not connected, initializing...")
                if not self.initialize():
                    logger.error("❌ LOGIN_DEBUG: Failed to initialize MT5")
                    return False

            # Get current terminal info before login
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                logger.error("❌ LOGIN_DEBUG: Cannot get terminal info before login")
                return False

            logger.info(f"🔍 LOGIN_DEBUG: Terminal info before login - Connected: {terminal_info.connected}, Build: {terminal_info.build}")

            # Check if already logged into the same account
            current_account_info = mt5.account_info()
            if current_account_info and current_account_info.login == account.account_number:
                logger.info(f"🔍 LOGIN_DEBUG: Already logged into account {account.account_number}")
                self.current_account = account
                trading_logger.log_account_status(account.account_id, "LOGIN_SUCCESS")
                return True

            # Log current account if different
            if current_account_info:
                logger.info(f"🔍 LOGIN_DEBUG: Currently logged into account {current_account_info.login}, switching to {account.account_number}")

            # Attempt login
            logger.info(f"🔍 LOGIN_DEBUG: Attempting login with server: {account.server}")
            authorized = mt5.login(
                login=account.account_number,
                password=account.password,
                server=account.server
            )

            if not authorized:
                error = mt5.last_error()
                error_msg = f"❌ LOGIN_DEBUG: Failed to login to account {account.account_number}. MT5 Error: {error}"
                logger.error(error_msg)

                # Additional diagnostics
                terminal_info_after = mt5.terminal_info()
                if terminal_info_after:
                    logger.error(f"🔍 LOGIN_DEBUG: Terminal status after failed login - Connected: {terminal_info_after.connected}")

                # Check if the server is available
                logger.error(f"🔍 LOGIN_DEBUG: Checking server availability for {account.server}")

                trading_logger.log_account_status(account.account_id, "LOGIN_FAILED")
                return False

            # Verify login was successful
            new_account_info = mt5.account_info()
            if new_account_info is None:
                error_msg = "❌ LOGIN_DEBUG: Login appeared successful but cannot get account info"
                logger.error(error_msg)
                trading_logger.log_account_status(account.account_id, "LOGIN_FAILED")
                return False

            if new_account_info.login != account.account_number:
                error_msg = f"❌ LOGIN_DEBUG: Login mismatch. Expected: {account.account_number}, Got: {new_account_info.login}"
                logger.error(error_msg)
                trading_logger.log_account_status(account.account_id, "LOGIN_FAILED")
                return False

            # Log successful login details
            logger.info(f"✅ LOGIN_DEBUG: Successfully logged in to account {account.account_number}")
            logger.info(f"🔍 LOGIN_DEBUG: Account details - Balance: {new_account_info.balance}, Equity: {new_account_info.equity}, Currency: {new_account_info.currency}")
            logger.info(f"🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: {new_account_info.trade_allowed}, Trade expert: {new_account_info.trade_expert}")

            self.current_account = account

            # Small delay to ensure connection is stable
            time.sleep(0.5)

            trading_logger.log_account_status(account.account_id, "LOGIN_SUCCESS")
            return True

        except Exception as e:
            error_msg = f"❌ LOGIN_DEBUG: Exception during login: {type(e).__name__}: {e}"
            logger.error(error_msg)
            logger.error(f"🔍 LOGIN_DEBUG: Exception traceback:", exc_info=True)
            trading_logger.log_account_status(account.account_id, "LOGIN_ERROR")
            return False
    
    def get_account_info(self) -> Optional[AccountBalance]:
        """Get current account balance and margin information"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return None
            
            account_info = mt5.account_info()
            if not account_info:
                error = mt5.last_error()
                logger.error(f"Failed to get account info: {error}")
                return None
            
            balance = AccountBalance(
                account_id=self.current_account.account_id,
                balance=account_info.balance,
                equity=account_info.equity,
                margin=account_info.margin,
                free_margin=account_info.margin_free,
                margin_level=account_info.margin_level,
                currency=account_info.currency,
                leverage=account_info.leverage
            )
            
            trading_logger.log_account_status(
                self.current_account.account_id,
                "BALANCE_UPDATE",
                balance.balance,
                balance.equity,
                balance.margin_level
            )
            
            return balance
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None
    
    def get_market_data(self, symbol: str, timeframe: str, count: int = 200) -> Optional[Dict[str, Any]]:
        """Get market data for analysis"""
        try:
            # Validate connection
            if not self.current_account:
                logger.error("No active MT5 connection. Please login first.")
                return None

            logger.debug(f"Retrieving market data for {symbol} {timeframe} ({count} candles)")

            # Convert timeframe string to MT5 constant
            tf_map = {
                'M1': mt5.TIMEFRAME_M1,
                'M5': mt5.TIMEFRAME_M5,
                'M15': mt5.TIMEFRAME_M15,
                'M30': mt5.TIMEFRAME_M30,
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1
            }

            mt5_timeframe = tf_map.get(timeframe)
            if not mt5_timeframe:
                logger.error(f"Unsupported timeframe: {timeframe}")
                return None
            
            # Check if symbol is available on this broker
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Symbol {symbol} not available on broker {self.current_account.server}")
                return None

            # Ensure symbol is selected in Market Watch
            if not mt5.symbol_select(symbol, True):
                logger.warning(f"Could not select symbol {symbol} in Market Watch")

            # Get rates with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
                if rates is not None and len(rates) > 0:
                    break

                error = mt5.last_error()
                logger.warning(f"Attempt {attempt + 1}/{max_retries} failed to get rates for {symbol}: {error}")

                if attempt < max_retries - 1:
                    time.sleep(1)  # Wait 1 second before retry
                else:
                    logger.error(f"Failed to get rates for {symbol} after {max_retries} attempts: {error}")
                    return None
            
            # Convert to list of dictionaries
            candles = []
            for rate in rates:
                candles.append({
                    'time': datetime.fromtimestamp(rate['time']).strftime('%Y-%m-%d %H:%M:%S'),
                    'open': float(rate['open']),
                    'high': float(rate['high']),
                    'low': float(rate['low']),
                    'close': float(rate['close']),
                    'volume': int(rate['tick_volume'])
                })
            
            # Get current tick
            tick = mt5.symbol_info_tick(symbol)
            if not tick:
                logger.warning(f"Could not get current tick for {symbol}")
                current_price = candles[-1]['close']
                spread = 0.0
            else:
                current_price = float(tick.ask)
                spread = float(tick.ask - tick.bid) / self._get_pip_size(symbol)
            
            # Calculate volatility (ATR-like)
            if len(candles) >= 14:
                volatility = self._calculate_volatility(candles[-14:])
            else:
                volatility = 0.0001
            
            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            pip_size = self._get_pip_size(symbol)
            pip_value = self._get_pip_value(symbol)
            
            market_data = {
                'symbol': symbol,
                'timeframe': timeframe,
                'candles': candles,
                'current_price': current_price,
                'spread': spread,
                'volume': candles[-1]['volume'] if candles else 0,
                'volatility': volatility,
                'pip_size': pip_size,
                'pip_value': pip_value,
                'min_volume': symbol_info.volume_min if symbol_info else 0.01,
                'max_volume': symbol_info.volume_max if symbol_info else 100.0,
                'timestamp': datetime.now().isoformat()
            }
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return None
    
    def place_order(
        self,
        symbol: str,
        action: str,
        volume: float,
        price: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        magic_number: int = 0,
        comment: str = ""
    ) -> Optional[int]:
        """Place a trading order with intensive debugging"""

        # Fix type issues - ensure all numeric parameters are proper types
        try:
            if price is not None:
                if isinstance(price, (list, tuple)):
                    price = float(price[0]) if price else None
                    logger.warning(f"⚠️ PLACE_ORDER_DEBUG: Price was list/tuple, converted to: {price}")
                else:
                    price = float(price) if price is not None else None

            if stop_loss is not None:
                if isinstance(stop_loss, (list, tuple)):
                    stop_loss = float(stop_loss[0]) if stop_loss else None
                    logger.warning(f"⚠️ PLACE_ORDER_DEBUG: Stop loss was list/tuple, converted to: {stop_loss}")
                else:
                    stop_loss = float(stop_loss) if stop_loss is not None else None

            if take_profit is not None:
                if isinstance(take_profit, (list, tuple)):
                    take_profit = float(take_profit[0]) if take_profit else None
                    logger.warning(f"⚠️ PLACE_ORDER_DEBUG: Take profit was list/tuple, converted to: {take_profit}")
                else:
                    take_profit = float(take_profit) if take_profit is not None else None

            volume = float(volume)

        except (ValueError, TypeError, IndexError) as e:
            error_msg = f"❌ PLACE_ORDER_DEBUG: Type conversion error: {e}"
            logger.error(error_msg)
            return None

        debug_info = {
            "symbol": symbol,
            "action": action,
            "volume": volume,
            "price": price,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "magic_number": magic_number,
            "comment": comment
        }

        logger.info(f"🔍 PLACE_ORDER_DEBUG: Starting order placement with params: {debug_info}")

        try:
            # Check account login status
            if not self.current_account:
                error_msg = "❌ PLACE_ORDER_DEBUG: No account logged in"
                logger.error(error_msg)
                return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Current account: {self.current_account.account_id} ({self.current_account.account_number})")

            # Check MT5 connection status
            if not self.connected:
                error_msg = "❌ PLACE_ORDER_DEBUG: MT5 not connected"
                logger.error(error_msg)
                return None

            # Verify MT5 terminal info
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                error_msg = "❌ PLACE_ORDER_DEBUG: Cannot get terminal info - MT5 connection lost"
                logger.error(error_msg)
                return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Terminal connected: {terminal_info.connected}, Trade allowed: {terminal_info.trade_allowed}")

            # Check account info
            account_info = mt5.account_info()
            if account_info is None:
                error_msg = "❌ PLACE_ORDER_DEBUG: Cannot get account info - account not logged in properly"
                logger.error(error_msg)
                return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Account info - Login: {account_info.login}, Trade allowed: {account_info.trade_allowed}, Trade expert: {account_info.trade_expert}")

            # Check symbol availability and info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                error_msg = f"❌ PLACE_ORDER_DEBUG: Symbol {symbol} not found or not available"
                logger.error(error_msg)
                return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Symbol {symbol} - Visible: {symbol_info.visible}, Trade mode: {symbol_info.trade_mode}")

            # Make symbol visible if not
            if not symbol_info.visible:
                logger.info(f"🔍 PLACE_ORDER_DEBUG: Making symbol {symbol} visible")
                if not mt5.symbol_select(symbol, True):
                    error_msg = f"❌ PLACE_ORDER_DEBUG: Failed to select symbol {symbol}"
                    logger.error(error_msg)
                    return None

            # Get current tick data
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                error_msg = f"❌ PLACE_ORDER_DEBUG: Cannot get tick data for {symbol}"
                logger.error(error_msg)
                return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Tick data - Bid: {tick.bid}, Ask: {tick.ask}, Spread: {tick.ask - tick.bid}")

            # Determine order type and handle price logic
            current_market_price = tick.ask if action.upper() == "BUY" else tick.bid
            price_tolerance = 0.0005  # 5 pips tolerance for major pairs

            if action.upper() == "BUY":
                order_type = mt5.ORDER_TYPE_BUY
                if price is None:
                    price = tick.ask
                    logger.info(f"🔍 PLACE_ORDER_DEBUG: BUY order, using current ask price: {price}")
                else:
                    price_diff = abs(price - tick.ask)
                    logger.info(f"🔍 PLACE_ORDER_DEBUG: BUY order - Entry: {price}, Current Ask: {tick.ask}, Diff: {price_diff}")

                    if price_diff <= price_tolerance:
                        # Small difference, use current market price
                        logger.info(f"🔍 PLACE_ORDER_DEBUG: Price difference small ({price_diff}), using current ask: {tick.ask}")
                        price = tick.ask
                    elif price < tick.ask:
                        # Entry price below current price, use pending BUY LIMIT
                        order_type = mt5.ORDER_TYPE_BUY_LIMIT
                        logger.info(f"🔍 PLACE_ORDER_DEBUG: Entry below market, using BUY LIMIT at {price}")
                    else:
                        # Entry price above current price, use pending BUY STOP
                        order_type = mt5.ORDER_TYPE_BUY_STOP
                        logger.info(f"🔍 PLACE_ORDER_DEBUG: Entry above market, using BUY STOP at {price}")

            elif action.upper() == "SELL":
                order_type = mt5.ORDER_TYPE_SELL
                if price is None:
                    price = tick.bid
                    logger.info(f"🔍 PLACE_ORDER_DEBUG: SELL order, using current bid price: {price}")
                else:
                    price_diff = abs(price - tick.bid)
                    logger.info(f"🔍 PLACE_ORDER_DEBUG: SELL order - Entry: {price}, Current Bid: {tick.bid}, Diff: {price_diff}")

                    if price_diff <= price_tolerance:
                        # Small difference, use current market price
                        logger.info(f"🔍 PLACE_ORDER_DEBUG: Price difference small ({price_diff}), using current bid: {tick.bid}")
                        price = tick.bid
                    elif price > tick.bid:
                        # Entry price above current price, use pending SELL LIMIT
                        order_type = mt5.ORDER_TYPE_SELL_LIMIT
                        logger.info(f"🔍 PLACE_ORDER_DEBUG: Entry above market, using SELL LIMIT at {price}")
                    else:
                        # Entry price below current price, use pending SELL STOP
                        order_type = mt5.ORDER_TYPE_SELL_STOP
                        logger.info(f"🔍 PLACE_ORDER_DEBUG: Entry below market, using SELL STOP at {price}")
            else:
                error_msg = f"❌ PLACE_ORDER_DEBUG: Invalid action: {action}"
                logger.error(error_msg)
                return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Order type determined: {order_type}, Final price: {price}")

            # Normalize all prices to correct decimal places
            order_type_map = {
                mt5.ORDER_TYPE_BUY: "BUY",
                mt5.ORDER_TYPE_SELL: "SELL",
                mt5.ORDER_TYPE_BUY_LIMIT: "BUY_LIMIT",
                mt5.ORDER_TYPE_SELL_LIMIT: "SELL_LIMIT",
                mt5.ORDER_TYPE_BUY_STOP: "BUY_STOP",
                mt5.ORDER_TYPE_SELL_STOP: "SELL_STOP"
            }
            order_type_str = order_type_map.get(order_type, "MARKET")

            original_price = price
            price = self._validate_price_for_symbol(symbol, price, order_type_str)
            if price != original_price:
                logger.info(f"🔍 PLACE_ORDER_DEBUG: Price normalized from {original_price} to {price}")

            if stop_loss is not None:
                original_sl = stop_loss
                stop_loss = self._normalize_price(symbol, stop_loss)
                if stop_loss != original_sl:
                    logger.info(f"🔍 PLACE_ORDER_DEBUG: Stop loss normalized from {original_sl} to {stop_loss}")

            if take_profit is not None:
                original_tp = take_profit
                take_profit = self._normalize_price(symbol, take_profit)
                if take_profit != original_tp:
                    logger.info(f"🔍 PLACE_ORDER_DEBUG: Take profit normalized from {original_tp} to {take_profit}")

            # Validate volume
            if volume < symbol_info.volume_min:
                error_msg = f"❌ PLACE_ORDER_DEBUG: Volume {volume} below minimum {symbol_info.volume_min}"
                logger.error(error_msg)
                return None

            if volume > symbol_info.volume_max:
                error_msg = f"❌ PLACE_ORDER_DEBUG: Volume {volume} above maximum {symbol_info.volume_max}"
                logger.error(error_msg)
                return None

            # Validate stop loss and take profit distances with improved logic
            stops_level = symbol_info.trade_stops_level
            point = symbol_info.point

            # Enhanced stop level handling for different brokers
            if stops_level == 0:
                # Different defaults based on symbol type and broker characteristics
                if 'JPY' in symbol:
                    stops_level = 30  # JPY pairs typically need larger distances
                elif symbol.endswith('!'):  # Some brokers append ! to symbols
                    stops_level = 20  # Conservative for broker-specific symbols
                else:
                    stops_level = 15  # More conservative default for major pairs
                logger.info(f"🔍 PLACE_ORDER_DEBUG: Stops level is 0, using enhanced default: {stops_level} for {symbol}")

            min_distance = stops_level * point
            logger.info(f"🔍 PLACE_ORDER_DEBUG: Symbol {symbol} - Stops level: {stops_level}, Point: {point}, Min distance: {min_distance}")

            # Enhanced stop loss validation with better precision handling
            if stop_loss is not None:
                # Normalize stop loss first to ensure proper precision
                stop_loss = self._normalize_price(symbol, stop_loss)
                sl_distance = abs(price - stop_loss)

                # Add small buffer to account for floating point precision issues
                min_distance_with_buffer = min_distance * 1.1  # 10% buffer

                if sl_distance < min_distance_with_buffer:
                    logger.warning(f"⚠️ PLACE_ORDER_DEBUG: Stop loss distance {sl_distance:.6f} < required {min_distance_with_buffer:.6f}")

                    # Calculate proper stop loss with enhanced distance
                    if action.upper() == "BUY":
                        adjusted_sl = price - min_distance_with_buffer
                    else:  # SELL
                        adjusted_sl = price + min_distance_with_buffer

                    adjusted_sl = self._normalize_price(symbol, adjusted_sl)
                    logger.warning(f"⚠️ PLACE_ORDER_DEBUG: Adjusting SL from {stop_loss} to {adjusted_sl} (distance: {abs(price - adjusted_sl):.6f})")
                    stop_loss = adjusted_sl
                    sl_distance = abs(price - stop_loss)

                logger.info(f"🔍 PLACE_ORDER_DEBUG: Stop loss validation passed. Final SL: {stop_loss}, Distance: {sl_distance:.6f}")

            # Enhanced take profit validation with better precision handling
            if take_profit is not None:
                # Normalize take profit first to ensure proper precision
                take_profit = self._normalize_price(symbol, take_profit)
                tp_distance = abs(price - take_profit)

                # Add small buffer to account for floating point precision issues
                min_distance_with_buffer = min_distance * 1.1  # 10% buffer

                if tp_distance < min_distance_with_buffer:
                    logger.warning(f"⚠️ PLACE_ORDER_DEBUG: Take profit distance {tp_distance:.6f} < required {min_distance_with_buffer:.6f}")

                    # Calculate proper take profit with enhanced distance
                    if action.upper() == "BUY":
                        adjusted_tp = price + min_distance_with_buffer
                    else:  # SELL
                        adjusted_tp = price - min_distance_with_buffer

                    adjusted_tp = self._normalize_price(symbol, adjusted_tp)
                    logger.warning(f"⚠️ PLACE_ORDER_DEBUG: Adjusting TP from {take_profit} to {adjusted_tp} (distance: {abs(price - adjusted_tp):.6f})")
                    take_profit = adjusted_tp
                    tp_distance = abs(price - take_profit)

                logger.info(f"🔍 PLACE_ORDER_DEBUG: Take profit validation passed. Final TP: {take_profit}, Distance: {tp_distance:.6f}")

            # Check for demo account limitations
            self._check_demo_account_limitations(symbol)

            # Create a meaningful but safe comment for RoboForex demo
            safe_comment = self._create_safe_comment(comment, action, symbol)

            # Prepare request based on order type
            if order_type in [mt5.ORDER_TYPE_BUY, mt5.ORDER_TYPE_SELL]:
                # Market order
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": volume,
                    "type": order_type,
                    "price": price,
                    "magic": magic_number,
                    "comment": safe_comment,
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_IOC,
                }
                logger.info(f"🔍 PLACE_ORDER_DEBUG: Preparing MARKET order")
            else:
                # Pending order
                request = {
                    "action": mt5.TRADE_ACTION_PENDING,
                    "symbol": symbol,
                    "volume": volume,
                    "type": order_type,
                    "price": price,
                    "magic": magic_number,
                    "comment": safe_comment,
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_RETURN,  # Better for pending orders
                }
                logger.info(f"🔍 PLACE_ORDER_DEBUG: Preparing PENDING order type: {order_type}")

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Using safe comment: '{safe_comment}' (RoboForex demo restriction)")

            # Add stop loss and take profit if provided
            if stop_loss:
                request["sl"] = stop_loss
            if take_profit:
                request["tp"] = take_profit

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Final request: {request}")

            # Check order before sending with enhanced diagnostics
            logger.info("🔍 PLACE_ORDER_DEBUG: Performing order check...")
            check_result = mt5.order_check(request)

            if check_result is None:
                # Get last error for more details
                last_error = mt5.last_error()
                error_msg = f"❌ PLACE_ORDER_DEBUG: order_check returned None. Last error: {last_error}"
                logger.error(error_msg)

                # Additional diagnostics for order_check failure
                logger.error(f"🔍 PLACE_ORDER_DEBUG: Request details: {request}")
                logger.error(f"🔍 PLACE_ORDER_DEBUG: Current tick - Bid: {tick.bid}, Ask: {tick.ask}")
                logger.error(f"🔍 PLACE_ORDER_DEBUG: Price difference - Request: {price}, Current Ask: {tick.ask}, Diff: {abs(price - tick.ask)}")

                # Check if market is open
                market_info = mt5.symbol_info(symbol)
                if market_info:
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Symbol info - Visible: {market_info.visible}, Trade mode: {market_info.trade_mode}")
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Symbol sessions - Session open: {getattr(market_info, 'session_open', 'N/A')}, Session close: {getattr(market_info, 'session_close', 'N/A')}")
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Symbol digits: {market_info.digits}, Point: {market_info.point}")

                # Try with current market price instead
                logger.info("🔍 PLACE_ORDER_DEBUG: Retrying with current market price...")
                request_retry = request.copy()
                request_retry["price"] = tick.ask if action.upper() == "BUY" else tick.bid

                check_result_retry = mt5.order_check(request_retry)
                if check_result_retry is None:
                    # Try with even simpler comment
                    logger.info("🔍 PLACE_ORDER_DEBUG: Trying with minimal comment...")
                    request_minimal = request_retry.copy()
                    request_minimal["comment"] = "AI"

                    check_result_minimal = mt5.order_check(request_minimal)
                    if check_result_minimal is None:
                        # Try without comment at all
                        logger.info("🔍 PLACE_ORDER_DEBUG: Trying without comment...")
                        request_no_comment = request_retry.copy()
                        del request_no_comment["comment"]

                        check_result_no_comment = mt5.order_check(request_no_comment)
                        if check_result_no_comment is None:
                            error_msg = f"❌ PLACE_ORDER_DEBUG: order_check returns None even without comment. Account may have restrictions."
                            logger.error(error_msg)

                            # Log account-specific diagnostics
                            account_info = mt5.account_info()
                            if account_info:
                                logger.error(f"🔍 PLACE_ORDER_DEBUG: Account diagnostics:")
                                logger.error(f"  - Trade mode: {account_info.trade_mode}")
                                logger.error(f"  - Trade allowed: {account_info.trade_allowed}")
                                logger.error(f"  - Trade expert: {account_info.trade_expert}")
                                logger.error(f"  - Server: {account_info.server}")
                                logger.error(f"  - Company: {account_info.company}")
                                logger.error(f"  - Balance: {account_info.balance}")
                                logger.error(f"  - Margin level: {account_info.margin_level}")

                            trading_logger.log_trade_execution(
                                self.current_account.account_id,
                                symbol,
                                action,
                                volume,
                                price,
                                success=False,
                                error=error_msg
                            )
                            return None
                        else:
                            logger.info(f"🔍 PLACE_ORDER_DEBUG: Order check passed without comment")
                            request = request_no_comment
                            check_result = check_result_no_comment
                    else:
                        logger.info(f"🔍 PLACE_ORDER_DEBUG: Order check passed with minimal comment 'AI'")
                        request = request_minimal
                        check_result = check_result_minimal
                else:
                    logger.info(f"🔍 PLACE_ORDER_DEBUG: Order check passed with current price: {request_retry['price']}")
                    request = request_retry  # Use the corrected request
                    check_result = check_result_retry

            # Check if order check passed (retcode 0 means success for order_check)
            if check_result.retcode != 0:
                error_msg = f"❌ PLACE_ORDER_DEBUG: Order check failed: {check_result.retcode} - {check_result.comment}"
                logger.error(error_msg)

                # Enhanced handling for error 10016 (Invalid stops) - NO HARDCODED FALLBACKS
                if check_result.retcode == 10016:
                    logger.error("🔍 PLACE_ORDER_DEBUG: Error 10016 - Invalid stops detected")
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Current request stops - SL: {request.get('sl', 'None')}, TP: {request.get('tp', 'None')}")
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Price: {request.get('price', 'None')}, Action: {request.get('action', 'None')}")
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Symbol stops level: {symbol_info.trade_stops_level}, Point: {symbol_info.point}")

                    # Calculate actual distances for debugging
                    if request.get('sl') and request.get('price'):
                        sl_distance = abs(float(request['price']) - float(request['sl']))
                        sl_distance_points = sl_distance / symbol_info.point
                        logger.error(f"🔍 PLACE_ORDER_DEBUG: SL distance: {sl_distance:.6f} ({sl_distance_points:.1f} points)")

                    if request.get('tp') and request.get('price'):
                        tp_distance = abs(float(request['price']) - float(request['tp']))
                        tp_distance_points = tp_distance / symbol_info.point
                        logger.error(f"🔍 PLACE_ORDER_DEBUG: TP distance: {tp_distance:.6f} ({tp_distance_points:.1f} points)")

                    # Try one more time with recalculated stops using more conservative distances
                    if request.get('sl') or request.get('tp'):
                        logger.warning("🔍 PLACE_ORDER_DEBUG: Attempting to recalculate stops with more conservative distances")
                        request_recalc = request.copy()

                        # Use more conservative minimum distance (double the required)
                        conservative_distance = (symbol_info.trade_stops_level or 15) * symbol_info.point * 2

                        if request.get('sl'):
                            if action.upper() == "BUY":
                                new_sl = float(request['price']) - conservative_distance
                            else:  # SELL
                                new_sl = float(request['price']) + conservative_distance
                            request_recalc['sl'] = self._normalize_price(symbol, new_sl)
                            logger.warning(f"🔍 PLACE_ORDER_DEBUG: Recalculated SL: {request_recalc['sl']} (distance: {conservative_distance:.6f})")

                        if request.get('tp'):
                            if action.upper() == "BUY":
                                new_tp = float(request['price']) + conservative_distance
                            else:  # SELL
                                new_tp = float(request['price']) - conservative_distance
                            request_recalc['tp'] = self._normalize_price(symbol, new_tp)
                            logger.warning(f"🔍 PLACE_ORDER_DEBUG: Recalculated TP: {request_recalc['tp']} (distance: {conservative_distance:.6f})")

                        check_result_recalc = mt5.order_check(request_recalc)
                        if check_result_recalc and check_result_recalc.retcode == 0:
                            logger.info("🔍 PLACE_ORDER_DEBUG: Order check passed with recalculated stops")
                            request = request_recalc
                            check_result = check_result_recalc
                        else:
                            logger.error(f"🔍 PLACE_ORDER_DEBUG: Order check failed even with recalculated stops: {check_result_recalc.retcode if check_result_recalc else 'None'}")

                    # REMOVED: No hardcoded fallback to remove stops - all decisions must be AI-driven

                # Log additional check result details (safely)
                if check_result.retcode != 0:  # Still failed after potential fixes
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Check result details - Margin: {getattr(check_result, 'margin', 'N/A')}, Free margin: {getattr(check_result, 'margin_free', 'N/A')}")
                    logger.error(f"🔍 PLACE_ORDER_DEBUG: Check result - Request ID: {getattr(check_result, 'request_id', 'N/A')}, Retcode: {check_result.retcode}")

                    trading_logger.log_trade_execution(
                        self.current_account.account_id,
                        symbol,
                        action,
                        volume,
                        price,
                        success=False,
                        error=error_msg
                    )
                    return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Order check passed! Retcode: {check_result.retcode} (Success)")
            logger.info(f"🔍 PLACE_ORDER_DEBUG: Margin required: {getattr(check_result, 'margin', 'N/A')}, Free margin: {getattr(check_result, 'margin_free', 'N/A')}")

            # Send order
            logger.info("🔍 PLACE_ORDER_DEBUG: Sending order to MT5...")
            result = mt5.order_send(request)

            if result is None:
                # Get last error for more details
                last_error = mt5.last_error()
                error_msg = f"❌ PLACE_ORDER_DEBUG: MT5 order_send returned None. Last error: {last_error}"
                logger.error(error_msg)

                # Additional diagnostics
                logger.error(f"🔍 PLACE_ORDER_DEBUG: Connection status: {mt5.terminal_info().connected if mt5.terminal_info() else 'Unknown'}")
                logger.error(f"🔍 PLACE_ORDER_DEBUG: Account login: {mt5.account_info().login if mt5.account_info() else 'Unknown'}")

                trading_logger.log_trade_execution(
                    self.current_account.account_id,
                    symbol,
                    action,
                    volume,
                    price,
                    success=False,
                    error=error_msg
                )
                return None

            logger.info(f"🔍 PLACE_ORDER_DEBUG: Order result received: {result}")

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"❌ PLACE_ORDER_DEBUG: Order failed - Retcode: {result.retcode}, Comment: {result.comment}, Request ID: {result.request_id}"
                logger.error(error_msg)

                # Log additional result details
                logger.error(f"🔍 PLACE_ORDER_DEBUG: Result details - Deal: {result.deal}, Order: {result.order}, Volume: {result.volume}, Price: {result.price}")

                trading_logger.log_trade_execution(
                    self.current_account.account_id,
                    symbol,
                    action,
                    volume,
                    price,
                    success=False,
                    error=error_msg
                )
                return None

            order_id = result.order
            logger.info(f"✅ PLACE_ORDER_DEBUG: Order placed successfully! Order ID: {order_id}, Deal ID: {result.deal}")
            trading_logger.log_trade_execution(
                self.current_account.account_id,
                symbol,
                action,
                volume,
                price,
                order_id,
                success=True
            )

            return order_id

        except Exception as e:
            error_msg = f"❌ PLACE_ORDER_DEBUG: Exception occurred: {type(e).__name__}: {e}"
            logger.error(error_msg)
            logger.error(f"🔍 PLACE_ORDER_DEBUG: Exception traceback:", exc_info=True)

            trading_logger.log_trade_execution(
                self.current_account.account_id if self.current_account else "unknown",
                symbol,
                action,
                volume,
                price or 0,
                success=False,
                error=error_msg
            )
            return None

    def get_positions(self) -> List[Dict[str, Any]]:
        """Get all open positions"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return []

            positions = mt5.positions_get()
            if positions is None:
                return []

            position_list = []
            for pos in positions:
                position_list.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': 'BUY' if pos.type == mt5.POSITION_TYPE_BUY else 'SELL',
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'price_current': pos.price_current,
                    'profit': pos.profit,
                    'swap': pos.swap,
                    'sl': pos.sl,
                    'tp': pos.tp,
                    'magic': pos.magic,
                    'comment': pos.comment,
                    'time': datetime.fromtimestamp(pos.time)
                })

            return position_list

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []

    def get_pending_orders(self) -> List[Dict[str, Any]]:
        """Get all pending orders"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return []

            # Get pending orders
            orders = mt5.orders_get()
            if orders is None:
                logger.debug("No pending orders found")
                return []

            pending_orders = []
            for order in orders:
                order_dict = {
                    'ticket': order.ticket,
                    'symbol': order.symbol,
                    'type': order.type,
                    'volume': order.volume_initial,
                    'price_open': order.price_open,
                    'sl': order.sl,
                    'tp': order.tp,
                    'magic': order.magic,
                    'comment': order.comment,
                    'time_setup': order.time_setup,
                    'type_time': order.type_time,
                    'type_filling': order.type_filling
                }
                pending_orders.append(order_dict)

            logger.debug(f"Retrieved {len(pending_orders)} pending orders")
            return pending_orders

        except Exception as e:
            logger.error(f"Error getting pending orders: {e}")
            return []

    def get_orders(self) -> List[Dict[str, Any]]:
        """Get all pending orders"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return []

            orders = mt5.orders_get()
            if orders is None:
                return []

            order_list = []
            for order in orders:
                order_list.append({
                    'ticket': order.ticket,
                    'symbol': order.symbol,
                    'type': self._get_order_type_string(order.type),
                    'volume': order.volume_initial,
                    'volume_current': order.volume_current,
                    'price_open': order.price_open,
                    'sl': order.sl,
                    'tp': order.tp,
                    'magic': order.magic,
                    'comment': order.comment,
                    'time_setup': datetime.fromtimestamp(order.time_setup),
                    'time_expiration': datetime.fromtimestamp(order.time_expiration) if order.time_expiration > 0 else None,
                    'state': self._get_order_state_string(order.state)
                })

            return order_list

        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []

    def _get_order_type_string(self, order_type: int) -> str:
        """Convert MT5 order type to string"""
        type_map = {
            mt5.ORDER_TYPE_BUY: 'BUY',
            mt5.ORDER_TYPE_SELL: 'SELL',
            mt5.ORDER_TYPE_BUY_LIMIT: 'BUY_LIMIT',
            mt5.ORDER_TYPE_SELL_LIMIT: 'SELL_LIMIT',
            mt5.ORDER_TYPE_BUY_STOP: 'BUY_STOP',
            mt5.ORDER_TYPE_SELL_STOP: 'SELL_STOP',
            mt5.ORDER_TYPE_BUY_STOP_LIMIT: 'BUY_STOP_LIMIT',
            mt5.ORDER_TYPE_SELL_STOP_LIMIT: 'SELL_STOP_LIMIT'
        }
        return type_map.get(order_type, f'UNKNOWN_{order_type}')

    def _get_order_state_string(self, state: int) -> str:
        """Convert MT5 order state to string"""
        state_map = {
            mt5.ORDER_STATE_STARTED: 'STARTED',
            mt5.ORDER_STATE_PLACED: 'PLACED',
            mt5.ORDER_STATE_CANCELED: 'CANCELED',
            mt5.ORDER_STATE_PARTIAL: 'PARTIAL',
            mt5.ORDER_STATE_FILLED: 'FILLED',
            mt5.ORDER_STATE_REJECTED: 'REJECTED',
            mt5.ORDER_STATE_EXPIRED: 'EXPIRED',
            mt5.ORDER_STATE_REQUEST_ADD: 'REQUEST_ADD',
            mt5.ORDER_STATE_REQUEST_MODIFY: 'REQUEST_MODIFY',
            mt5.ORDER_STATE_REQUEST_CANCEL: 'REQUEST_CANCEL'
        }
        return state_map.get(state, f'UNKNOWN_{state}')

    def close_position(self, ticket: int) -> bool:
        """Close a specific position"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return False

            # Get position info
            position = mt5.positions_get(ticket=ticket)
            if not position:
                logger.error(f"Position {ticket} not found")
                return False

            pos = position[0]

            # Determine close order type
            if pos.type == mt5.POSITION_TYPE_BUY:
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(pos.symbol).bid
            else:
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(pos.symbol).ask

            # Prepare close request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": pos.symbol,
                "volume": pos.volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "magic": pos.magic,
                "comment": f"Close position {ticket}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # Send close order
            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"Close failed: {result.retcode} - {result.comment}"
                logger.error(error_msg)
                return False

            # Calculate profit
            profit = pos.profit + pos.swap

            trading_logger.log_trade_close(
                self.current_account.account_id,
                ticket,
                pos.symbol,
                pos.volume,
                pos.price_open,
                price,
                profit,
                "AI Decision"
            )

            logger.info(f"Position {ticket} closed successfully")
            return True

        except Exception as e:
            logger.error(f"Error closing position {ticket}: {e}")
            return False

    def modify_position(
        self,
        ticket: int,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None
    ) -> bool:
        """Modify position stop loss and/or take profit"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return False

            # Get position info
            position = mt5.positions_get(ticket=ticket)
            if not position:
                logger.error(f"Position {ticket} not found")
                return False

            pos = position[0]
            symbol = pos.symbol

            logger.info(f"🔍 MODIFY_POSITION_DEBUG: Modifying position {ticket} for {symbol}")
            logger.info(f"🔍 MODIFY_POSITION_DEBUG: Original - SL: {pos.sl}, TP: {pos.tp}")

            # Normalize prices
            final_sl = pos.sl
            final_tp = pos.tp

            if stop_loss is not None:
                final_sl = self._normalize_price(symbol, stop_loss)
                logger.info(f"🔍 MODIFY_POSITION_DEBUG: Stop loss normalized from {stop_loss} to {final_sl}")

            if take_profit is not None:
                final_tp = self._normalize_price(symbol, take_profit)
                logger.info(f"🔍 MODIFY_POSITION_DEBUG: Take profit normalized from {take_profit} to {final_tp}")

            # Check if values are actually different (prevent "No changes" error 10025)
            current_sl = self._normalize_price(symbol, pos.sl) if pos.sl != 0.0 else 0.0
            current_tp = self._normalize_price(symbol, pos.tp) if pos.tp != 0.0 else 0.0

            # Compare normalized values to avoid unnecessary modifications
            sl_changed = abs(final_sl - current_sl) > 0.000001 if final_sl != 0.0 and current_sl != 0.0 else final_sl != current_sl
            tp_changed = abs(final_tp - current_tp) > 0.000001 if final_tp != 0.0 and current_tp != 0.0 else final_tp != current_tp

            if not sl_changed and not tp_changed:
                logger.info(f"🔍 MODIFY_POSITION_DEBUG: No changes needed for position {ticket} - SL: {final_sl} == {current_sl}, TP: {final_tp} == {current_tp}")
                return True  # Return success since no modification is needed

            logger.info(f"🔍 MODIFY_POSITION_DEBUG: Changes detected - SL changed: {sl_changed}, TP changed: {tp_changed}")

            # Prepare modification request
            request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": symbol,
                "position": ticket,
                "sl": final_sl,
                "tp": final_tp,
            }

            logger.info(f"🔍 MODIFY_POSITION_DEBUG: Final request - SL: {final_sl}, TP: {final_tp}")

            # Send modification
            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"Position modification failed: {result.retcode} - {result.comment}"
                logger.error(error_msg)

                # Enhanced error handling for specific error codes
                if result.retcode == 10015:  # Invalid price
                    logger.error(f"❌ MODIFY_POSITION_DEBUG: Invalid price error for position {ticket}")
                    logger.error(f"🔍 MODIFY_POSITION_DEBUG: Attempted SL: {final_sl}, TP: {final_tp}")

                    # Get current market data for context
                    tick = mt5.symbol_info_tick(symbol)
                    if tick:
                        logger.error(f"🔍 MODIFY_POSITION_DEBUG: Current market - Bid: {tick.bid}, Ask: {tick.ask}")
                        logger.error(f"🔍 MODIFY_POSITION_DEBUG: Position entry price: {pos.price_open}, Current price: {pos.price_current}")

                elif result.retcode == 10025:  # No changes
                    logger.info(f"🔍 MODIFY_POSITION_DEBUG: MT5 reports no changes needed for position {ticket} (error 10025)")
                    logger.info(f"🔍 MODIFY_POSITION_DEBUG: This should have been caught by pre-check, but MT5 still detected no changes")
                    return True  # Treat as success since no change was needed

                return False

            # Log modifications
            if stop_loss and stop_loss != pos.sl:
                trading_logger.log_trade_modification(
                    self.current_account.account_id,
                    ticket,
                    "Stop Loss",
                    pos.sl,
                    stop_loss,
                    success=True
                )

            if take_profit and take_profit != pos.tp:
                trading_logger.log_trade_modification(
                    self.current_account.account_id,
                    ticket,
                    "Take Profit",
                    pos.tp,
                    take_profit,
                    success=True
                )

            logger.info(f"Position {ticket} modified successfully")
            return True

        except Exception as e:
            logger.error(f"Error modifying position {ticket}: {e}")
            return False

    def cancel_order(self, ticket: int) -> bool:
        """Cancel a pending order"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return False

            # Get order info
            orders = mt5.orders_get(ticket=ticket)
            if not orders:
                logger.error(f"Order {ticket} not found")
                return False

            order = orders[0]

            # Prepare cancellation request
            request = {
                "action": mt5.TRADE_ACTION_REMOVE,
                "order": ticket,
            }

            # Send cancellation
            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"Order cancellation failed: {result.retcode} - {result.comment}"
                logger.error(error_msg)
                return False

            # Log cancellation
            trading_logger.log_pending_order(
                self.current_account.account_id,
                order.symbol,
                f"CANCEL_{self._get_order_type_string(order.type)}",
                order.volume_initial,
                order.price_open,
                order.sl,
                order.tp,
                ticket,
                success=True
            )

            logger.info(f"Order {ticket} cancelled successfully")
            return True

        except Exception as e:
            logger.error(f"Error cancelling order {ticket}: {e}")
            return False

    def modify_order(
        self,
        ticket: int,
        price: Optional[float] = None,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None
    ) -> bool:
        """Modify a pending order's price, stop loss, and/or take profit"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return False

            # Get order info
            orders = mt5.orders_get(ticket=ticket)
            if not orders:
                logger.error(f"Order {ticket} not found")
                return False

            order = orders[0]
            symbol = order.symbol

            logger.info(f"🔍 MODIFY_ORDER_DEBUG: Modifying order {ticket} for {symbol}")
            logger.info(f"🔍 MODIFY_ORDER_DEBUG: Original - Price: {order.price_open}, SL: {order.sl}, TP: {order.tp}")

            # Normalize and validate prices
            final_price = order.price_open
            final_sl = order.sl
            final_tp = order.tp

            if price is not None:
                # Determine order type for price validation
                order_type_map = {
                    mt5.ORDER_TYPE_BUY_LIMIT: "BUY_LIMIT",
                    mt5.ORDER_TYPE_SELL_LIMIT: "SELL_LIMIT",
                    mt5.ORDER_TYPE_BUY_STOP: "BUY_STOP",
                    mt5.ORDER_TYPE_SELL_STOP: "SELL_STOP"
                }
                order_type_str = order_type_map.get(order.type, "MARKET")
                final_price = self._validate_price_for_symbol(symbol, price, order_type_str)
                logger.info(f"🔍 MODIFY_ORDER_DEBUG: Price normalized from {price} to {final_price}")

            if stop_loss is not None:
                final_sl = self._normalize_price(symbol, stop_loss)
                logger.info(f"🔍 MODIFY_ORDER_DEBUG: Stop loss normalized from {stop_loss} to {final_sl}")

            if take_profit is not None:
                final_tp = self._normalize_price(symbol, take_profit)
                logger.info(f"🔍 MODIFY_ORDER_DEBUG: Take profit normalized from {take_profit} to {final_tp}")

            # Prepare modification request
            request = {
                "action": mt5.TRADE_ACTION_MODIFY,
                "order": ticket,
                "price": final_price,
                "sl": final_sl,
                "tp": final_tp,
            }

            logger.info(f"🔍 MODIFY_ORDER_DEBUG: Final request - Price: {final_price}, SL: {final_sl}, TP: {final_tp}")

            # Send modification
            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"Order modification failed: {result.retcode} - {result.comment}"
                logger.error(error_msg)

                # Enhanced error handling for specific error codes
                if result.retcode == 10015:  # Invalid price
                    logger.error(f"❌ MODIFY_ORDER_DEBUG: Invalid price error - trying with current market prices")

                    # Get current market data
                    tick = mt5.symbol_info_tick(symbol)
                    if tick:
                        logger.error(f"🔍 MODIFY_ORDER_DEBUG: Current market - Bid: {tick.bid}, Ask: {tick.ask}")

                        # Try with market-based prices if it was a price modification
                        if price is not None:
                            market_price = tick.ask if order.type in [mt5.ORDER_TYPE_BUY_LIMIT, mt5.ORDER_TYPE_BUY_STOP] else tick.bid
                            retry_price = self._normalize_price(symbol, market_price)

                            retry_request = request.copy()
                            retry_request["price"] = retry_price

                            logger.info(f"🔍 MODIFY_ORDER_DEBUG: Retrying with market price: {retry_price}")
                            retry_result = mt5.order_send(retry_request)

                            if retry_result.retcode == mt5.TRADE_RETCODE_DONE:
                                logger.info(f"✅ MODIFY_ORDER_DEBUG: Retry successful with market price")
                                final_price = retry_price
                            else:
                                logger.error(f"❌ MODIFY_ORDER_DEBUG: Retry also failed: {retry_result.retcode} - {retry_result.comment}")
                                return False
                        else:
                            return False
                    else:
                        return False
                else:
                    return False

            # Log modifications
            if price is not None and price != order.price_open:
                trading_logger.log_trade_modification(
                    self.current_account.account_id,
                    ticket,
                    "Order Price",
                    order.price_open,
                    price,
                    success=True
                )

            if stop_loss is not None and stop_loss != order.sl:
                trading_logger.log_trade_modification(
                    self.current_account.account_id,
                    ticket,
                    "Order Stop Loss",
                    order.sl,
                    stop_loss,
                    success=True
                )

            if take_profit is not None and take_profit != order.tp:
                trading_logger.log_trade_modification(
                    self.current_account.account_id,
                    ticket,
                    "Order Take Profit",
                    order.tp,
                    take_profit,
                    success=True
                )

            logger.info(f"Order {ticket} modified successfully")
            return True

        except Exception as e:
            logger.error(f"Error modifying order {ticket}: {e}")
            return False

    def get_trade_history(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get trade history for specified number of days"""
        try:
            if not self.current_account:
                logger.error("No account logged in")
                return []

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Get deals (completed trades)
            deals = mt5.history_deals_get(start_date, end_date)
            if deals is None:
                return []

            trade_history = []
            for deal in deals:
                if deal.type in [mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL]:
                    trade_history.append({
                        'ticket': deal.ticket,
                        'order': deal.order,
                        'symbol': deal.symbol,
                        'type': 'BUY' if deal.type == mt5.DEAL_TYPE_BUY else 'SELL',
                        'volume': deal.volume,
                        'price': deal.price,
                        'profit': deal.profit,
                        'swap': deal.swap,
                        'commission': deal.commission,
                        'magic_number': deal.magic,
                        'comment': deal.comment,
                        'time': datetime.fromtimestamp(deal.time),
                        'entry_price': deal.price,  # For compatibility
                        'exit_price': deal.price,   # For compatibility
                        'duration_hours': 0,        # Will be calculated if needed
                        'exit_reason': 'Completed'
                    })

            return trade_history

        except Exception as e:
            logger.error(f"Error getting trade history: {e}")
            return []

    def _get_pip_size(self, symbol: str) -> float:
        """Get pip size for symbol"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info:
                # For JPY pairs, pip is usually 0.01 (2nd decimal place)
                if 'JPY' in symbol:
                    return 0.01
                # For most other forex pairs, pip is 0.0001 (4th decimal place)
                else:
                    return 0.0001
            return 0.0001  # Default for forex
        except:
            return 0.0001

    def _get_pip_value(self, symbol: str) -> float:
        """Get pip value for symbol (value of 1 pip for 1 lot in account currency)"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                return 10.0  # Default for major pairs

            # Get tick value and tick size
            tick_value = symbol_info.trade_tick_value  # Value of 1 tick for 1 lot
            tick_size = symbol_info.trade_tick_size    # Size of 1 tick

            # Calculate pip size
            pip_size = self._get_pip_size(symbol)

            # Calculate how many ticks make 1 pip
            ticks_per_pip = pip_size / tick_size if tick_size > 0 else 1

            # Calculate pip value (value of 1 pip for 1 lot)
            pip_value = tick_value * ticks_per_pip

            logger.debug(f"Pip value calculation for {symbol}: tick_value={tick_value}, tick_size={tick_size}, pip_size={pip_size}, ticks_per_pip={ticks_per_pip}, pip_value={pip_value}")

            return pip_value

        except Exception as e:
            logger.error(f"Error calculating pip value for {symbol}: {e}")
            # Default pip values for common pairs
            if 'JPY' in symbol:
                return 6.77  # Approximate for JPY pairs
            else:
                return 10.0  # Standard for major pairs like EURUSD, GBPUSD

    def _normalize_price(self, symbol: str, price: float) -> float:
        """Normalize price to the correct number of decimal places for the symbol"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info and symbol_info.digits > 0:
                # Round to the symbol's digit precision
                normalized_price = round(price, symbol_info.digits)
                logger.debug(f"🔍 PRICE_NORMALIZE: {symbol} - Original: {price}, Normalized: {normalized_price}, Digits: {symbol_info.digits}")
                return normalized_price
            return round(price, 5)  # Default to 5 decimal places for forex
        except Exception as e:
            logger.warning(f"⚠️ PRICE_NORMALIZE: Error normalizing price for {symbol}: {e}, using original price: {price}")
            return round(price, 5)

    def _validate_price_for_symbol(self, symbol: str, price: float, order_type: str = "MARKET") -> float:
        """Validate and adjust price according to symbol specifications"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                logger.warning(f"⚠️ PRICE_VALIDATE: Cannot get symbol info for {symbol}, using original price")
                return self._normalize_price(symbol, price)

            # Normalize to correct decimal places
            normalized_price = self._normalize_price(symbol, price)

            # Check if price is within valid trading range
            if hasattr(symbol_info, 'bid') and hasattr(symbol_info, 'ask'):
                # For market orders, ensure price is reasonable relative to current market
                current_spread = symbol_info.ask - symbol_info.bid
                max_deviation = current_spread * 10  # Allow up to 10x spread deviation

                if order_type.upper() in ["BUY", "BUY_LIMIT", "BUY_STOP"]:
                    if abs(normalized_price - symbol_info.ask) > max_deviation:
                        logger.warning(f"⚠️ PRICE_VALIDATE: {symbol} BUY price {normalized_price} too far from ask {symbol_info.ask}, adjusting")
                        normalized_price = symbol_info.ask
                elif order_type.upper() in ["SELL", "SELL_LIMIT", "SELL_STOP"]:
                    if abs(normalized_price - symbol_info.bid) > max_deviation:
                        logger.warning(f"⚠️ PRICE_VALIDATE: {symbol} SELL price {normalized_price} too far from bid {symbol_info.bid}, adjusting")
                        normalized_price = symbol_info.bid

            # Final normalization after any adjustments
            final_price = self._normalize_price(symbol, normalized_price)

            if final_price != price:
                logger.info(f"🔍 PRICE_VALIDATE: {symbol} - Original: {price}, Final: {final_price}")

            return final_price

        except Exception as e:
            logger.error(f"❌ PRICE_VALIDATE: Error validating price for {symbol}: {e}")
            return self._normalize_price(symbol, price)

    def _calculate_volatility(self, candles: List[Dict[str, Any]]) -> float:
        """Calculate volatility (simplified ATR)"""
        try:
            if len(candles) < 2:
                return 0.0001

            true_ranges = []
            for i in range(1, len(candles)):
                high = candles[i]['high']
                low = candles[i]['low']
                prev_close = candles[i-1]['close']

                tr = max(
                    high - low,
                    abs(high - prev_close),
                    abs(low - prev_close)
                )
                true_ranges.append(tr)

            return sum(true_ranges) / len(true_ranges) if true_ranges else 0.0001

        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.0001

    def _check_demo_account_limitations(self, symbol: str):
        """Check for demo account limitations that might affect trading"""
        try:
            account_info = mt5.account_info()
            if account_info is None:
                logger.warning("⚠️ DEMO_CHECK: Cannot get account info")
                return

            # Check if it's a demo account
            is_demo = account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_DEMO
            logger.info(f"🔍 DEMO_CHECK: Account type - Demo: {is_demo}, Trade mode: {account_info.trade_mode}")

            if is_demo:
                logger.info("🔍 DEMO_CHECK: Demo account detected - checking limitations...")

                # Check balance limitations
                if account_info.balance <= 0:
                    logger.warning(f"⚠️ DEMO_CHECK: Zero or negative balance: {account_info.balance}")

                # Check margin limitations
                if account_info.margin_level < 100 and account_info.margin > 0:
                    logger.warning(f"⚠️ DEMO_CHECK: Low margin level: {account_info.margin_level}%")

                # Check symbol-specific limitations for demo
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info:
                    logger.info(f"🔍 DEMO_CHECK: Symbol {symbol} - Min volume: {symbol_info.volume_min}, Max volume: {symbol_info.volume_max}")
                    logger.info(f"🔍 DEMO_CHECK: Symbol {symbol} - Trade mode: {symbol_info.trade_mode}, Calc mode: {getattr(symbol_info, 'calc_mode', 'N/A')}")

                    # Check if symbol allows trading
                    if symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_DISABLED:
                        logger.warning(f"⚠️ DEMO_CHECK: Trading disabled for {symbol}")
                    elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_LONGONLY:
                        logger.info(f"🔍 DEMO_CHECK: {symbol} allows LONG positions only")
                    elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_SHORTONLY:
                        logger.info(f"🔍 DEMO_CHECK: {symbol} allows SHORT positions only")
                    elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_CLOSEONLY:
                        logger.warning(f"⚠️ DEMO_CHECK: {symbol} allows CLOSE only")

                # Check server limitations
                logger.info(f"🔍 DEMO_CHECK: Server: {account_info.server}, Company: {account_info.company}")

                # RoboForex specific checks
                if 'robo' in account_info.company.lower() or 'robo' in account_info.server.lower():
                    logger.info("🔍 DEMO_CHECK: RoboForex demo account detected")
                    logger.info("🔍 DEMO_CHECK: RoboForex demo limitations:")
                    logger.info("  - Some symbols may have restricted trading hours")
                    logger.info("  - Pending orders may have minimum distance requirements")
                    logger.info("  - Some EAs may be restricted")

                    # Check for common RoboForex demo restrictions
                    if symbol_info and symbol_info.trade_stops_level > 0:
                        logger.info(f"🔍 DEMO_CHECK: Minimum stop level: {symbol_info.trade_stops_level} points")
                        min_distance = symbol_info.trade_stops_level * symbol_info.point
                        logger.info(f"🔍 DEMO_CHECK: Minimum stop distance: {min_distance}")

            else:
                logger.info("🔍 DEMO_CHECK: Real account detected")

        except Exception as e:
            logger.error(f"❌ DEMO_CHECK: Error checking demo limitations: {e}")

    def _sanitize_comment(self, comment: str) -> str:
        """Sanitize comment for MT5 compatibility"""
        try:
            if not comment:
                return "AI_Trade"

            logger.info(f"🔍 SANITIZE: Original comment: '{comment}' (length: {len(comment)})")

            # Remove invalid characters and limit length
            # MT5 typically allows up to 31 characters for comments
            sanitized = ''.join(c for c in comment if c.isalnum() or c in ['_', '-', '.'])
            logger.info(f"🔍 SANITIZE: After character filter: '{sanitized}' (length: {len(sanitized)})")

            # Limit to 31 characters (MT5 limit)
            if len(sanitized) > 31:
                sanitized = sanitized[:31]
                logger.info(f"🔍 SANITIZE: After length limit: '{sanitized}' (length: {len(sanitized)})")

            # Ensure it's not empty after sanitization
            if not sanitized:
                sanitized = "AI_Trade"
                logger.info(f"🔍 SANITIZE: Using fallback: '{sanitized}'")

            logger.info(f"🔍 SANITIZE: Final result: '{sanitized}'")
            return sanitized

        except Exception as e:
            logger.error(f"❌ Error sanitizing comment: {e}")
            return "AI_Trade"

    def _create_safe_comment(self, original_comment: str, action: str, symbol: str) -> str:
        """Create a safe but meaningful comment for RoboForex demo accounts"""
        try:
            # Extract key information from original comment
            if "trend_following" in original_comment.lower():
                strategy = "TF"  # Trend Following
            elif "scalping" in original_comment.lower():
                strategy = "SC"  # Scalping
            elif "swing" in original_comment.lower():
                strategy = "SW"  # Swing
            else:
                strategy = "AI"  # Default

            # Create short but meaningful comment (max 15 chars for safety)
            # Format: AI_TF_BUY_EUR (AI_Strategy_Action_Currency)
            currency = symbol[:3] if len(symbol) >= 3 else symbol
            safe_comment = f"AI_{strategy}_{action[:1]}_{currency}"

            # Ensure it's within safe limits (15 chars max)
            if len(safe_comment) > 15:
                safe_comment = f"AI_{action[:1]}_{currency}"

            # Final fallback if still too long
            if len(safe_comment) > 15:
                safe_comment = "AI"

            logger.info(f"🔍 SAFE_COMMENT: '{original_comment}' -> '{safe_comment}' (RoboForex compatible)")
            return safe_comment

        except Exception as e:
            logger.error(f"❌ Error creating safe comment: {e}")
            return "AI"
